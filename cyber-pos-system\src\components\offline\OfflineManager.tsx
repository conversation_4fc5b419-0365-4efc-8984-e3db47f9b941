import React, { useState, useEffect } from 'react';
import {
  Wifi,
  WifiOff,
  Sync,
  Alert<PERSON>ircle,
  CheckCircle,
  Clock,
  X
} from 'lucide-react';

interface OfflineQueueItem {
  url: string;
  method: string;
  headers: Record<string, string>;
  body: string | null;
  timestamp: number;
  description?: string;
}

const OfflineManager: React.FC = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [offlineQueue, setOfflineQueue] = useState<OfflineQueueItem[]>([]);
  const [showOfflineIndicator, setShowOfflineIndicator] = useState(false);
  const [syncInProgress, setSyncInProgress] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);

  // Monitor online/offline status
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setShowOfflineIndicator(false);
      // Trigger sync when coming back online
      if (offlineQueue.length > 0) {
        syncOfflineData();
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      setShowOfflineIndicator(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [offlineQueue.length]);

  // Load offline queue from localStorage
  useEffect(() => {
    const loadOfflineQueue = () => {
      try {
        const stored = localStorage.getItem('offline-queue');
        if (stored) {
          const queue = JSON.parse(stored);
          setOfflineQueue(queue);
        }
      } catch (error) {
        console.error('Error loading offline queue:', error);
      }
    };

    loadOfflineQueue();

    // Listen for storage changes (from service worker)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'offline-queue') {
        loadOfflineQueue();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // Listen for service worker messages
  useEffect(() => {
    const handleServiceWorkerMessage = (event: MessageEvent) => {
      if (event.data?.type === 'OFFLINE_SYNC_COMPLETE') {
        setSyncInProgress(false);
        setLastSyncTime(new Date());
        
        // Reload offline queue
        const stored = localStorage.getItem('offline-queue');
        if (stored) {
          setOfflineQueue(JSON.parse(stored));
        } else {
          setOfflineQueue([]);
        }

        // Show success notification
        showNotification(
          `Sync completed! ${event.data.processedCount} items synchronized.`,
          'success'
        );
      }
    };

    navigator.serviceWorker?.addEventListener('message', handleServiceWorkerMessage);
    return () => {
      navigator.serviceWorker?.removeEventListener('message', handleServiceWorkerMessage);
    };
  }, []);

  // Manually trigger sync
  const syncOfflineData = async () => {
    if (!isOnline || syncInProgress) return;

    setSyncInProgress(true);
    
    try {
      // Send message to service worker to process queue
      if (navigator.serviceWorker?.controller) {
        navigator.serviceWorker.controller.postMessage({
          type: 'PROCESS_OFFLINE_QUEUE'
        });
      }
    } catch (error) {
      console.error('Error triggering sync:', error);
      setSyncInProgress(false);
      showNotification('Sync failed. Please try again.', 'error');
    }
  };

  // Clear offline queue
  const clearOfflineQueue = () => {
    localStorage.removeItem('offline-queue');
    setOfflineQueue([]);
    showNotification('Offline queue cleared.', 'success');
  };

  // Show notification
  const showNotification = (message: string, type: 'success' | 'error' | 'info') => {
    // This would integrate with your notification system
    console.log(`${type.toUpperCase()}: ${message}`);
  };

  // Format timestamp
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  // Get description for queued item
  const getItemDescription = (item: OfflineQueueItem) => {
    if (item.description) return item.description;
    
    // Generate description based on URL and method
    const url = new URL(item.url);
    const path = url.pathname;
    
    if (path.includes('/transactions')) {
      return item.method === 'POST' ? 'New transaction' : 'Transaction update';
    } else if (path.includes('/products')) {
      return item.method === 'POST' ? 'New product' : 'Product update';
    } else if (path.includes('/services')) {
      return item.method === 'POST' ? 'New service' : 'Service update';
    }
    
    return `${item.method} ${path}`;
  };

  return (
    <>
      {/* Offline Indicator */}
      {showOfflineIndicator && (
        <div className="fixed top-0 left-0 right-0 bg-orange-500 text-white px-4 py-2 text-center text-sm font-medium z-50">
          <div className="flex items-center justify-center">
            <WifiOff className="h-4 w-4 mr-2" />
            You are currently offline. Changes will be synced when connection is restored.
            {offlineQueue.length > 0 && (
              <span className="ml-2 bg-orange-600 px-2 py-1 rounded text-xs">
                {offlineQueue.length} pending
              </span>
            )}
          </div>
        </div>
      )}

      {/* Connection Status in Corner */}
      <div className="fixed bottom-4 right-4 z-40">
        <div className={`flex items-center px-3 py-2 rounded-lg shadow-lg text-sm font-medium ${
          isOnline 
            ? 'bg-green-100 text-green-800 border border-green-200' 
            : 'bg-red-100 text-red-800 border border-red-200'
        }`}>
          {isOnline ? (
            <>
              <Wifi className="h-4 w-4 mr-2" />
              Online
            </>
          ) : (
            <>
              <WifiOff className="h-4 w-4 mr-2" />
              Offline
            </>
          )}
          
          {offlineQueue.length > 0 && (
            <span className="ml-2 bg-gray-600 text-white px-2 py-1 rounded text-xs">
              {offlineQueue.length}
            </span>
          )}
        </div>
      </div>

      {/* Offline Queue Panel */}
      {offlineQueue.length > 0 && (
        <div className="fixed bottom-20 right-4 w-80 bg-white rounded-lg shadow-xl border border-gray-200 z-40">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-semibold text-gray-900 flex items-center">
                <Clock className="h-4 w-4 mr-2" />
                Pending Sync ({offlineQueue.length})
              </h3>
              <div className="flex space-x-2">
                {isOnline && (
                  <button
                    onClick={syncOfflineData}
                    disabled={syncInProgress}
                    className="text-blue-600 hover:text-blue-800 disabled:opacity-50"
                    title="Sync now"
                  >
                    <Sync className={`h-4 w-4 ${syncInProgress ? 'animate-spin' : ''}`} />
                  </button>
                )}
                <button
                  onClick={clearOfflineQueue}
                  className="text-red-600 hover:text-red-800"
                  title="Clear queue"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>
            
            {lastSyncTime && (
              <p className="text-xs text-gray-500 mt-1">
                Last sync: {lastSyncTime.toLocaleTimeString()}
              </p>
            )}
          </div>
          
          <div className="max-h-64 overflow-y-auto">
            {offlineQueue.map((item, index) => (
              <div key={index} className="p-3 border-b border-gray-100 last:border-b-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">
                      {getItemDescription(item)}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {formatTimestamp(item.timestamp)}
                    </p>
                  </div>
                  <div className="flex items-center ml-2">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      item.method === 'POST' 
                        ? 'bg-green-100 text-green-800'
                        : item.method === 'PUT'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {item.method}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {isOnline && offlineQueue.length > 0 && (
            <div className="p-3 bg-gray-50 rounded-b-lg">
              <button
                onClick={syncOfflineData}
                disabled={syncInProgress}
                className="w-full bg-blue-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center"
              >
                {syncInProgress ? (
                  <>
                    <Sync className="h-4 w-4 mr-2 animate-spin" />
                    Syncing...
                  </>
                ) : (
                  <>
                    <Sync className="h-4 w-4 mr-2" />
                    Sync All
                  </>
                )}
              </button>
            </div>
          )}
        </div>
      )}

      {/* Sync Status Notifications */}
      {syncInProgress && (
        <div className="fixed bottom-4 left-4 bg-blue-100 border border-blue-200 text-blue-800 px-4 py-2 rounded-lg shadow-lg z-40">
          <div className="flex items-center">
            <Sync className="h-4 w-4 mr-2 animate-spin" />
            <span className="text-sm font-medium">Syncing offline data...</span>
          </div>
        </div>
      )}
    </>
  );
};

export default OfflineManager;
