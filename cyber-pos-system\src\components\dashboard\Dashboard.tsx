import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useDashboard } from '../../hooks/useDashboard';
import {
  DollarSign,
  ShoppingCart,
  Package,
  Users,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';

const Dashboard: React.FC = () => {
  const { currentUser } = useAuth();
  const {
    stats,
    recentTransactions,
    lowStockItems,
    loading,
    error,
    refreshData
  } = useDashboard();

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Format change percentage
  const formatChange = (change: number) => {
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change}%`;
  };

  // Get change type and icon
  const getChangeDisplay = (change: number) => {
    const isPositive = change >= 0;
    return {
      type: isPositive ? 'increase' : 'decrease',
      icon: isPositive ? TrendingUp : TrendingDown,
      color: isPositive ? 'text-green-600' : 'text-red-600',
    };
  };

  // Prepare stats data for display
  const statsData = [
    {
      name: 'Today\'s Sales',
      value: formatCurrency(stats.todaysSales),
      change: formatChange(stats.salesChange),
      changeDisplay: getChangeDisplay(stats.salesChange),
      icon: DollarSign,
    },
    {
      name: 'Transactions',
      value: stats.todaysTransactions.toString(),
      change: formatChange(stats.transactionsChange),
      changeDisplay: getChangeDisplay(stats.transactionsChange),
      icon: ShoppingCart,
    },
    {
      name: 'Low Stock Items',
      value: stats.lowStockCount.toString(),
      change: stats.lowStockCount > 0 ? 'Needs attention' : 'All good',
      changeDisplay: {
        type: stats.lowStockCount > 0 ? 'decrease' : 'increase',
        icon: AlertTriangle,
        color: stats.lowStockCount > 0 ? 'text-orange-600' : 'text-green-600',
      },
      icon: Package,
    },
    {
      name: 'Active Customers',
      value: stats.activeCustomers.toString(),
      change: 'Today',
      changeDisplay: {
        type: 'increase',
        icon: TrendingUp,
        color: 'text-blue-600',
      },
      icon: Users,
    },
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/3 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </div>
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="animate-pulse">
                  <div className="h-6 bg-gray-200 rounded w-1/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-400 mr-2" />
            <p className="text-red-800">Error loading dashboard: {error}</p>
            <button
              onClick={refreshData}
              className="ml-auto text-red-600 hover:text-red-800"
            >
              <RefreshCw className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Welcome back, {currentUser?.name}!
              </h1>
              <p className="mt-1 text-sm text-gray-500">
                Here's what's happening with your business today.
              </p>
            </div>
            <button
              onClick={refreshData}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {statsData.map((stat) => (
          <div key={stat.name} className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <stat.icon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {stat.name}
                    </dt>
                    <dd className="flex items-baseline">
                      <div className="text-2xl font-semibold text-gray-900">
                        {stat.value}
                      </div>
                      <div className={`ml-2 flex items-baseline text-sm font-semibold ${stat.changeDisplay.color}`}>
                        <stat.changeDisplay.icon className="self-center flex-shrink-0 h-4 w-4" />
                        <span className="ml-1">{stat.change}</span>
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Transactions */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Recent Transactions
            </h3>
            <div className="space-y-3">
              {recentTransactions.length > 0 ? (
                recentTransactions.map((transaction) => (
                  <div key={transaction.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="text-sm font-medium text-gray-900">{transaction.customerName}</p>
                      <p className="text-sm text-gray-500">{transaction.type} • {transaction.time}</p>
                    </div>
                    <div className="text-sm font-semibold text-gray-900">
                      {formatCurrency(transaction.amount)}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4">
                  <p className="text-sm text-gray-500">No recent transactions</p>
                </div>
              )}
            </div>
            <div className="mt-4">
              <button className="text-sm text-primary-600 hover:text-primary-500 font-medium">
                View all transactions →
              </button>
            </div>
          </div>
        </div>

        {/* Low Stock Alerts */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center mb-4">
              <AlertTriangle className="h-5 w-5 text-orange-500 mr-2" />
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Low Stock Alerts
              </h3>
            </div>
            <div className="space-y-3">
              {lowStockItems.length > 0 ? (
                lowStockItems.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200">
                    <div>
                      <p className="text-sm font-medium text-gray-900">{item.name}</p>
                      <p className="text-sm text-gray-500">
                        Current: {item.current} | Minimum: {item.minimum}
                      </p>
                    </div>
                    <div className="text-sm font-semibold text-orange-600">
                      Reorder
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4">
                  <p className="text-sm text-green-600">All products are well stocked!</p>
                </div>
              )}
            </div>
            <div className="mt-4">
              <button className="text-sm text-primary-600 hover:text-primary-500 font-medium">
                Manage inventory →
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
