# Firebase Configuration for Development
# Updated with actual Firebase service account configuration

# Firebase Project Configuration (from service account)
REACT_APP_FIREBASE_API_KEY=ba0bf70db7d90b9374430cf2be54ca8411c5c340
REACT_APP_FIREBASE_AUTH_DOMAIN=cyber-pos-system.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=cyber-pos-system
REACT_APP_FIREBASE_STORAGE_BUCKET=cyber-pos-system.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=117758163494852327626
REACT_APP_FIREBASE_APP_ID=cyber-pos-system

# Firebase Service Account Configuration (for admin operations)
FIREBASE_PROJECT_ID=cyber-pos-system
FIREBASE_PRIVATE_KEY_ID=ba0bf70db7d90b9374430cf2be54ca8411c5c340
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=117758163494852327626
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# Environment
REACT_APP_ENVIRONMENT=development

# Firebase Emulator Configuration
REACT_APP_USE_FIREBASE_EMULATOR=true
REACT_APP_FIREBASE_AUTH_EMULATOR_HOST=localhost:9099
REACT_APP_FIREBASE_FIRESTORE_EMULATOR_HOST=localhost:8080
REACT_APP_FIREBASE_STORAGE_EMULATOR_HOST=localhost:9199

# Note: This configuration now includes actual Firebase service account credentials
# For production builds, set REACT_APP_USE_FIREBASE_EMULATOR=false
# Keep the service account credentials secure and never commit them to public repositories
